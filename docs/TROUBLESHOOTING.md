# Troubleshooting Guide

This document provides solutions to common issues you might encounter when running the Thena Slack App.

## Service Dependencies

The Thena Slack App depends on several external services to function correctly:

### Required Services

1. **Platform API** (http://localhost:8000)
   - Main platform API service
   - Handles ticket creation, comments, and user management
   - **Critical**: A<PERSON> will not function without this service

2. **Apps Platform API** (http://localhost:8002)
   - Apps platform integration service
   - Handles app installations and platform events

3. **Annotator API** (http://localhost:8004)
   - AI annotation service
   - Provides AI-powered features

### Checking Service Health

Before starting the Slack app, always check if all required services are running:

```bash
npm run check-services
```

This script will:
- Check if each service is available
- Show which services are running/not running
- Provide instructions on how to start missing services

## Common Issues

### 1. Platform API Service Unavailable

**Symptoms:**
- Error messages like "Platform API service unavailable"
- Network errors with "fetch failed" or "ECONNREFUSED"
- Ticket creation fails
- Comments not syncing to platform

**Error Examples:**
```
[THENA_PLATFORM_API_PROVIDER] Network error for POST http://localhost:8000/v1/tickets: fetch failed
Platform API service unavailable. Please check if the service is running at http://localhost:8000
```

**Solution:**
1. Check if Platform API is running:
   ```bash
   npm run check-services
   ```

2. If Platform API is not running, start it:
   ```bash
   # In the platform repository
   npm install
   npm run start:dev
   ```

3. Verify the service is running:
   ```bash
   curl http://localhost:8000/health
   ```

### 2. Database Connection Issues

**Symptoms:**
- "column distinctAlias.Users_id does not exist" errors
- TypeORM query failures

**Solution:**
This was a known issue with TypeORM queries that has been fixed. If you still encounter this:

1. Ensure your database is running
2. Check database connection settings in `.env`
3. Run database migrations if needed

### 3. User Platform Lookup Failures

**Symptoms:**
- "Failed to lookup platform user info" errors
- X-User-ID header not being set correctly

**Solution:**
This issue has been resolved by improving the database query. The system now uses a QueryBuilder approach instead of the problematic `findOne` with `select` and relations.

### 4. Service Timeout Issues

**Symptoms:**
- "Platform API request timeout" errors
- Slow response times

**Solution:**
1. Check if services are overloaded
2. Restart the platform services
3. Check system resources (CPU, memory)

## Error Handling Improvements

The app now includes improved error handling:

### Graceful Degradation
- When Platform API is unavailable, users receive friendly error messages
- The app continues to function for non-platform dependent features
- Detailed logging helps with debugging

### User-Friendly Messages
Instead of technical errors, users now see:
```
⚠️ Service Temporarily Unavailable

The ticket creation service is currently unavailable. 
Please try again in a few moments or contact your administrator.
```

### Better Logging
- Clear error categorization (connection refused, timeout, etc.)
- Specific instructions for each error type
- Detailed context for debugging

## Development Workflow

### Before Starting Development

1. **Check all services:**
   ```bash
   npm run check-services
   ```

2. **Start missing services** as indicated by the check

3. **Verify database connection:**
   ```bash
   npm run typeorm migration:show
   ```

4. **Start the Slack app:**
   ```bash
   npm run start:dev
   ```

### Debugging Network Issues

1. **Check service logs** for each dependent service
2. **Use the health check script** to verify connectivity
3. **Check environment variables** in `.env` file
4. **Verify port availability:**
   ```bash
   lsof -i :8000 -i :8002 -i :8004
   ```

## Environment Configuration

Ensure your `.env` file has the correct service URLs:

```env
# Platform API
PLATFORM_API_URL=http://localhost:8000

# Apps Platform
APPS_PLATFORM_API_URL=http://localhost:8002

# Annotator API
ANNOTATOR_API_URL=http://localhost:8004
```

## Getting Help

If you continue to experience issues:

1. Run the service health check: `npm run check-services`
2. Check the application logs for detailed error messages
3. Verify all environment variables are set correctly
4. Ensure all dependent services are running and healthy

For additional support, check the main README.md or contact the development team.
