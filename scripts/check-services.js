#!/usr/bin/env node

/**
 * Service Health Check Script
 * 
 * This script checks if the required services are running and provides
 * instructions on how to start them if they're not available.
 */

const http = require('http');
const https = require('https');

// Service configurations from .env
const services = [
  {
    name: 'Platform API',
    url: 'http://localhost:8000/health',
    description: 'Main platform API service',
    startCommand: 'npm run start:dev (in platform repository)',
  },
  {
    name: 'Apps Platform API',
    url: 'http://localhost:8002/health',
    description: 'Apps platform integration service',
    startCommand: 'npm run start:dev (in apps-platform repository)',
  },
  {
    name: 'Annotator API',
    url: 'http://localhost:8004/health',
    description: 'AI annotation service',
    startCommand: 'npm run start:dev (in annotator repository)',
  },
];

/**
 * Check if a service is available
 * @param {Object} service Service configuration
 * @returns {Promise<boolean>} True if service is available
 */
function checkService(service) {
  return new Promise((resolve) => {
    const url = new URL(service.url);
    const client = url.protocol === 'https:' ? https : http;
    
    const req = client.request({
      hostname: url.hostname,
      port: url.port,
      path: url.pathname,
      method: 'GET',
      timeout: 5000,
    }, (res) => {
      resolve(res.statusCode >= 200 && res.statusCode < 300);
    });

    req.on('error', () => {
      resolve(false);
    });

    req.on('timeout', () => {
      req.destroy();
      resolve(false);
    });

    req.end();
  });
}

/**
 * Main function to check all services
 */
async function main() {
  console.log('🔍 Checking service health...\n');

  const results = [];
  
  for (const service of services) {
    process.stdout.write(`Checking ${service.name}... `);
    const isAvailable = await checkService(service);
    
    if (isAvailable) {
      console.log('✅ Available');
      results.push({ ...service, status: 'available' });
    } else {
      console.log('❌ Unavailable');
      results.push({ ...service, status: 'unavailable' });
    }
  }

  console.log('\n📊 Service Status Summary:');
  console.log('=' .repeat(50));

  const unavailableServices = results.filter(s => s.status === 'unavailable');
  const availableServices = results.filter(s => s.status === 'available');

  if (availableServices.length > 0) {
    console.log('\n✅ Available Services:');
    availableServices.forEach(service => {
      console.log(`  • ${service.name} - ${service.description}`);
    });
  }

  if (unavailableServices.length > 0) {
    console.log('\n❌ Unavailable Services:');
    unavailableServices.forEach(service => {
      console.log(`  • ${service.name} - ${service.description}`);
      console.log(`    URL: ${service.url}`);
      console.log(`    Start with: ${service.startCommand}`);
      console.log('');
    });

    console.log('⚠️  Some services are unavailable. The Slack app may not function correctly.');
    console.log('   Please start the missing services before using the Slack app.\n');
    
    console.log('💡 Quick Start Guide:');
    console.log('   1. Clone the required repositories');
    console.log('   2. Install dependencies: npm install');
    console.log('   3. Set up environment variables');
    console.log('   4. Start each service: npm run start:dev');
    console.log('   5. Run this script again to verify\n');
    
    process.exit(1);
  } else {
    console.log('\n🎉 All services are available! The Slack app should work correctly.\n');
    process.exit(0);
  }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Run the main function
main().catch((error) => {
  console.error('Error checking services:', error);
  process.exit(1);
});
