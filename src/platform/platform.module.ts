import { Module, OnModuleInit } from '@nestjs/common';
import { DiscoveryModule, DiscoveryService } from '@nestjs/core';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CommonModule } from '../common/common.module';
import { ConfigModule } from '../config/config.module';
import {
  CommentConversationMappings,
  CustomerContacts,
  GroupedSlackMessages,
  Installations,
  Organizations,
  PlatformTeams,
  PlatformTeamsToChannelMappings,
  Settings,
  SlackMessages,
  SlackTriageMessages,
  Users,
} from '../database/entities';
import { CustomerContactRepository } from '../database/entities/customer-contacts/repositories';
import { InstallationRepository } from '../database/entities/installations/repositories';
import { CommentConversationMapsRepository } from '../database/entities/mappings/repositories/comment-conversation-maps.repository';
import { SettingsRepository } from '../database/entities/settings';
import { GroupedSlackMessagesRepository } from '../database/entities/slack-messages/repositories/grouped-slack-messages.repository';
import { SlackMessagesRepository } from '../database/entities/slack-messages/repositories/slack-messages.repository';
import { SlackTriageMessagesRepository } from '../database/entities/slack-messages/repositories/slack-triage-messages.repository';
import { TeamsRepository } from '../database/entities/teams';
import { ThenaPlatformApiProvider } from '../external/provider/thena-platform-api.provider';
import { UserPlatformLookupService } from '../slack/services/user-platform-lookup.service';
import { SettingsCore } from '../slack/core';
import { SlackMessageCore } from '../slack/core/messages/slack-message.core';
import { SlackModule } from '../slack/slack.module';
import { EVENT_METADATA_KEY } from './decorators/platform-event.decorator';
import { eventHandlers } from './event-handlers';
import { PlatformController } from './platform.controller';
import { PlatformService } from './services/platform.service';

@Module({
  imports: [
    ConfigModule,
    CommonModule,
    DiscoveryModule,
    SlackModule,
    TypeOrmModule.forFeature([
      Users,
      Organizations,
      PlatformTeamsToChannelMappings,
      CustomerContacts,

      // Triage Messages
      SlackTriageMessages,
      SlackTriageMessagesRepository,

      // Grouped Slack Messages
      GroupedSlackMessages,
      GroupedSlackMessagesRepository,

      // Installation Repository
      Installations,
      InstallationRepository,

      // Slack Messages Repository
      SlackMessages,
      SlackMessagesRepository,

      // Teams Repository
      PlatformTeams,
      TeamsRepository,

      // Settings
      Settings,
      SettingsRepository,

      // Comment Conversation Maps
      CommentConversationMappings,
      CommentConversationMapsRepository,
    ]),
  ],
  controllers: [PlatformController],
  providers: [
    PlatformService,
    SettingsRepository,
    TeamsRepository,
    ThenaPlatformApiProvider,
    UserPlatformLookupService,
    SettingsCore,
    GroupedSlackMessagesRepository,
    CommentConversationMapsRepository,
    SlackMessagesRepository,
    SlackMessageCore,
    SlackTriageMessagesRepository,
    InstallationRepository,
    CustomerContactRepository,
    ...eventHandlers,
  ],
  exports: [PlatformService],
})
export class PlatformModule implements OnModuleInit {
  private eventHandlers: Map<string, any>;

  constructor(
    private readonly discoveryService: DiscoveryService,
    private readonly platformService: PlatformService,
  ) {}

  onModuleInit() {
    const providers = this.discoveryService.getProviders();
    const handlers = new Map<string, any>();

    for (const wrapper of providers) {
      if (
        !wrapper.metatype ||
        !Reflect.hasMetadata(EVENT_METADATA_KEY, wrapper.metatype)
      ) {
        continue;
      }

      const events: string[] = Reflect.getMetadata(
        EVENT_METADATA_KEY,
        wrapper.metatype,
      );

      for (const eventType of events) {
        handlers.set(eventType, wrapper);
      }
    }

    this.platformService.setEventHandlers(handlers);
  }
}
