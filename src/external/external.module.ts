import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CommonModule } from '../common/common.module';
import { ConfigModule } from '../config/config.module';
import { Users } from '../database/entities';
import { UserPlatformLookupService } from '../slack/services/user-platform-lookup.service';
import { ExternalService } from './external.service';
import { ThenaPlatformApiProvider } from './provider/thena-platform-api.provider';

@Module({
  imports: [ConfigModule, CommonModule, TypeOrmModule.forFeature([Users])],
  providers: [ExternalService, ThenaPlatformApiProvider, UserPlatformLookupService],
  exports: [ThenaPlatformApiProvider, UserPlatformLookupService],
})
export class ExternalModule {}
